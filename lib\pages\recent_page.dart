import 'dart:io';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:social_media_app/%20controllers/media_controller.dart';
import 'package:social_media_app/pages/audio_player_page.dart';
import '../widgets/media_card.dart';
import '../widgets/gradient_background.dart';

class RecentPage extends StatelessWidget {
  const RecentPage({super.key});

  @override
  Widget build(BuildContext context) {
    final mediaController = Get.find<MediaController>();

    return Scaffold(
      appBar: AppBar(
        title: const Text('المشغلة مؤخراً'),
        backgroundColor: Colors.blue.shade800,
      ),
      body: GradientBackground(
        child: Obx(() {
          final recent = mediaController.recentAudio;
          if (recent.isEmpty) {
            return const Center(
              child: Text('لا توجد ملفات صوتية مشغلة مؤخراً'),
            );
          }

          return ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: recent.length,
            itemBuilder: (context, index) {
              final item = recent[index];
              return MediaCard(
                item: item,
                onTap: () {
                  mediaController.addToRecent(item);
                  Get.to(() => UnifiedAudioPlayerPage(audioFile: File(item.path)));
                },
                showMoreOptions: true,
              );
            },
          );
        }),
      ),
    );
  }
}
