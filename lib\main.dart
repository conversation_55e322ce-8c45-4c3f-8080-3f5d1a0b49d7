import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:hive_flutter/hive_flutter.dart';


import ' controllers/audio_controller.dart';
import ' controllers/audio_playlist_controller.dart';
import ' controllers/media_controller.dart';
import ' controllers/playlist_controller.dart dart.dart';
import 'model/audio_playlist_model.dart';
import 'model/playlist_model.dart';
import 'pages/main_navigation_page.dart';
import ' controllers/theme_controller.dart';
import 'themes/app_themes.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await Hive.initFlutter();
  Hive.registerAdapter(AudioPlaylistAdapter());
  Hive.registerAdapter(PlaylistAdapter());
  await Hive.openBox<Playlist>('playlists');
  await Hive.openBox<AudioPlaylist>('audioPlaylists');

  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return GetMaterialApp(
      title: 'Lark Player',
      debugShowCheckedModeBanner: false,
      theme: AppThemes.darkTheme,
      home: const MainNavigationPage(),
      initialBinding: BindingsBuilder(() {
        Get.put(ThemeController());
        Get.put(PlaylistController());
        Get.put(MediaController());
        Get.put(AudioController());
        Get.put(AudioPlaylistController());
      }),
    );
  }
  }
