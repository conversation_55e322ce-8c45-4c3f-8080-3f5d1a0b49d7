import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../ controllers/audio_controller.dart';
import '../ controllers/audio_playlist_controller.dart';
import '../ controllers/playlist_controller.dart dart.dart';
import 'playlist_videos_page.dart';

class PlaylistPage extends StatelessWidget {
  const PlaylistPage({super.key});

  @override
  Widget build(BuildContext context) {
    final playlistController = Get.find<PlaylistController>();

    final TextEditingController newPlaylistController = TextEditingController();

    return Scaffold(
      body: Obx(() {
        final playlists = playlistController.playlists;
        if (playlists.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(Icons.playlist_add, size: 50),
                const SizedBox(height: 20),
                const Text('لا توجد قوائم تشغيل'),
                const SizedBox(height: 20),
                ElevatedButton(
                  onPressed: () => _showAddPlaylistDialog(
                      context, playlistController, newPlaylistController),
                  child: const Text('إنشاء قائمة جديدة'),
                ),
              ],
            ),
          );
        }
        return ListView.builder(
          itemCount: playlists.length,
          itemBuilder: (context, index) {
            final playlist = playlists[index];
            return Card(
              margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              child: ListTile(
                leading: const Icon(Icons.playlist_play),
                title: Text(playlist.name),
                subtitle: Text('${playlist.videoPaths.length} فيديو'),
                trailing: PopupMenuButton<String>(
                  onSelected: (value) {
                    if (value == 'delete') {
                      playlistController.deletePlaylist(index);
                    } else if (value == 'rename') {
                      _showRenameDialog(
                          context, playlistController, index, playlist.name);
                    }
                  },
                  itemBuilder: (context) => [
                    const PopupMenuItem(
                        value: 'rename', child: Text('تعديل الاسم')),
                    const PopupMenuItem(
                        value: 'delete', child: Text('حذف القائمة')),
                  ],
                ),
                onTap: () {
                  Get.to(() => PlaylistVideosPage(playlistIndex: index));
                },
              ),
            );
          },
        );
      }),
      floatingActionButton: FloatingActionButton(
        onPressed: () => _showAddPlaylistDialog(
            context, playlistController, newPlaylistController),
        tooltip: 'إضافة قائمة تشغيل',
        child: const Icon(Icons.add),
      ),
    );
  }

  void _showAddPlaylistDialog(BuildContext context,
      PlaylistController controller, TextEditingController textController) {
    Get.defaultDialog(
      title: 'إنشاء قائمة تشغيل جديدة',
      content: TextField(
        controller: textController,
        decoration: const InputDecoration(hintText: 'اسم القائمة'),
      ),
      onConfirm: () {
        final name = textController.text.trim();
        if (name.isNotEmpty) {
          controller.addPlaylist(name);
          textController.clear();
          Get.back();
        }
      },
      onCancel: () {
        textController.clear();
        Get.back();
      },
      textConfirm: 'إنشاء',
      textCancel: 'إلغاء',
    );
  }

  void _showRenameDialog(
      BuildContext context,
      PlaylistController controller,
      int index,
      String currentName) {
    final renameController = TextEditingController(text: currentName);
    Get.defaultDialog(
      title: 'تعديل اسم القائمة',
      content: TextField(
        controller: renameController,
        decoration: const InputDecoration(hintText: 'الاسم الجديد'),
      ),
      onConfirm: () {
        final newName = renameController.text.trim();
        if (newName.isNotEmpty) {
          controller.renamePlaylist(index, newName);
          Get.back();
        }
      },
      onCancel: () {
        Get.back();
      },
      textConfirm: 'حفظ',
      textCancel: 'إلغاء',
    );
  }
}


// ignore: use_key_in_widget_constructors
class AudioPlaylistsPage extends StatelessWidget {
  final audioPlaylistController = Get.put(AudioPlaylistController());
  final audioController = Get.find<AudioController>();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Obx(() {
        final playlists = audioPlaylistController.playlists;
        if (playlists.isEmpty) {
          return const Center(child: Text("لا توجد قوائم تشغيل."));
        }

        return ListView.builder(
          itemCount: playlists.length,
          itemBuilder: (context, index) {
            final playlist = playlists[index];
            return ListTile(
              title: Text(playlist.name),
              trailing: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  IconButton(
                    icon: const Icon(Icons.edit),
                    onPressed: () => _renamePlaylistDialog(context, index, playlist.name),
                  ),
                  IconButton(
                    icon: const Icon(Icons.delete),
                    onPressed: () => audioPlaylistController.deletePlaylist(index),
                  ),
                ],
              ),
              onTap: () => _showPlaylistSongs(context, playlist.songIds),
            );
          },
        );
      }),
      floatingActionButton: FloatingActionButton(
        child: const Icon(Icons.add),
        onPressed: () => _createPlaylistDialog(context),
      ),
    );
  }

  void _createPlaylistDialog(BuildContext context) {
    final nameController = TextEditingController();
    showDialog(
      context: context,
      builder: (_) => AlertDialog(
        title: const Text("إنشاء قائمة تشغيل"),
        content: TextField(
          controller: nameController,
          decoration: const InputDecoration(hintText: "اسم القائمة"),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text("إلغاء"),
          ),
          ElevatedButton(
            onPressed: () {
              final name = nameController.text.trim();
              if (name.isNotEmpty) {
                audioPlaylistController.addPlaylist(name);
              }
              Navigator.pop(context);
            },
            child: const Text("إنشاء"),
          ),
        ],
      ),
    );
  }

  void _renamePlaylistDialog(BuildContext context, int index, String currentName) {
    final nameController = TextEditingController(text: currentName);
    showDialog(
      context: context,
      builder: (_) => AlertDialog(
        title: const Text("إعادة تسمية القائمة"),
        content: TextField(
          controller: nameController,
          decoration: const InputDecoration(hintText: "الاسم الجديد"),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text("إلغاء"),
          ),
          ElevatedButton(
            onPressed: () {
              final name = nameController.text.trim();
              if (name.isNotEmpty) {
                audioPlaylistController.renamePlaylist(index, name);
              }
              Navigator.pop(context);
            },
            child: const Text("تأكيد"),
          ),
        ],
      ),
    );
  }

  void _showPlaylistSongs(BuildContext context, List<int> songIds) {
    final songs = audioController.allSongs.where((song) => songIds.contains(song.id)).toList();

    Get.bottomSheet(
      Container(
        padding: const EdgeInsets.all(16),
        height: 400,
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
        ),
        child: Column(
          children: [
            const Text("الأغاني في هذه القائمة", style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
            const SizedBox(height: 10),
            Expanded(
              child: ListView.builder(
                itemCount: songs.length,
                itemBuilder: (context, index) {
                  final song = songs[index];
                  return ListTile(
                    title: Text(song.title),
                    subtitle: Text(song.artist ?? "غير معروف"),
                    onTap: () {
                      audioController.playSong(song);
                      Get.back(); // إغلاق الـ bottom sheet
                    },
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}
