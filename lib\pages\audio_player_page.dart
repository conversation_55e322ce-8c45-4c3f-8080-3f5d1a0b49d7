import 'dart:io';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:just_audio/just_audio.dart';
import 'package:audio_session/audio_session.dart';
import 'package:share_plus/share_plus.dart';
import 'package:sliding_up_panel/sliding_up_panel.dart';
import 'package:social_media_app/%20controllers/audio_controller.dart';
import 'package:social_media_app/%20controllers/media_controller.dart';
import 'package:social_media_app/model/media_item.dart';
import 'package:social_media_app/pages/lyrics_page.dart';
import 'package:social_media_app/widgets/mini_player.dart';
import 'package:social_media_app/widgets/full_player.dart';

class UnifiedAudioPlayerPage extends StatefulWidget {
  final File audioFile;

  const UnifiedAudioPlayerPage({super.key, required this.audioFile});

  @override
  State<UnifiedAudioPlayerPage> createState() => _UnifiedAudioPlayerPageState();
}

class _UnifiedAudioPlayerPageState extends State<UnifiedAudioPlayerPage>
    with TickerProviderStateMixin {
  final player = AudioPlayer();
  final panelController = PanelController();

  late AudioController audioController;
  late MediaController mediaController;
  late AnimationController rotationController;

  Duration duration = Duration.zero;
  Duration position = Duration.zero;
  bool isLoading = true;
  bool isFavorite = false;
  List<MediaItem> playlist = [];
  int currentIndex = 0;

  @override
  void initState() {
    super.initState();
    audioController = Get.put(AudioController());
    mediaController = Get.put(MediaController());

    rotationController = AnimationController(vsync: this, duration: const Duration(seconds: 10));

    _initPlayer();
  }

  void _initPlayer() async {
    final session = await AudioSession.instance;
    await session.configure(const AudioSessionConfiguration.music());

    playlist = mediaController.allAudioFiles;
    currentIndex = playlist.indexWhere((e) => e.path == widget.audioFile.path);
    if (currentIndex == -1) currentIndex = 0;

    await player.setAudioSource(AudioSource.uri(Uri.file(widget.audioFile.path)));

    player.durationStream.listen((d) {
      if (mounted) setState(() => duration = d ?? Duration.zero);
    });

    player.positionStream.listen((p) {
      if (mounted) setState(() => position = p);
    });

    player.playerStateStream.listen((state) {
      if (mounted) {
        setState(() => isLoading = state.processingState == ProcessingState.loading);
        if (state.playing) {
          rotationController.repeat();
        } else {
          rotationController.stop();
        }
        if (state.processingState == ProcessingState.completed) {
          _playNext();
        }
      }
    });

    if (mounted) setState(() => isLoading = false);
  }

  void _togglePlayPause() {
    player.playing ? player.pause() : player.play();
  }

  void _playNext() {
    if (playlist.isEmpty) return;
    int nextIndex = (currentIndex + 1) % playlist.length;
    _changeTrack(nextIndex);
  }

  void _playPrevious() {
    if (playlist.isEmpty) return;
    int prevIndex = (currentIndex - 1);
    if (prevIndex < 0) prevIndex = playlist.length - 1;
    _changeTrack(prevIndex);
  }

  Future<void> _changeTrack(int index) async {
    if (index < 0 || index >= playlist.length) return;
    currentIndex = index;
    final newFile = File(playlist[index].path);
    await player.setAudioSource(AudioSource.uri(Uri.file(newFile.path)));
    await player.play();
  }

  void _toggleFavorite() {
    final current = playlist[currentIndex];
    setState(() {
      isFavorite = !isFavorite;
    });
    if (isFavorite) {
      mediaController.addToFavorites(current);
    } else {
      mediaController.removeFromFavorites(current);
    }
  }

  void _shareAudio() async {
    try {
      await Share.shareXFiles([XFile(widget.audioFile.path)], text: 'استمع لهذه الأغنية!');
    } catch (e) {
      Get.snackbar('خطأ', 'تعذرت مشاركة الملف');
    }
  }

  void _showFileInfo() {
    final file = widget.audioFile;
    final fileSize = (file.lengthSync() / (1024 * 1024)).toStringAsFixed(2);

    Get.dialog(
      AlertDialog(
        title: const Text('معلومات الملف'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('الاسم: ${file.path.split('/').last}'),
            Text('الحجم: $fileSize MB'),
            Text('المدة: ${_formatDuration(duration)}'),
            Text('المسار: ${file.path}'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  String _formatDuration(Duration d) {
    String two(int n) => n.toString().padLeft(2, '0');
    return '${two(d.inMinutes)}:${two(d.inSeconds.remainder(60))}';
  }

  @override
  void dispose() {
    player.dispose();
    rotationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey.shade900,
      body: SlidingUpPanel(
        controller: panelController,
        minHeight: 70,
        maxHeight: MediaQuery.of(context).size.height * 0.9,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
        collapsed:  MiniPlayer(),
        panel: FullPlayer(panelController: panelController),
        body: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            child: Column(
              children: [
                _buildHeader(),
                Expanded(
                  child: Center(
                    child: isLoading
                        ? const CircularProgressIndicator(color: Colors.white)
                        : Icon(Icons.music_note, size: 150, color: Colors.white30),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      children: [
        IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Get.back(),
        ),
        const Spacer(),
        IconButton(
          icon: const Icon(Icons.lyrics, color: Colors.white),
          onPressed: () => Get.to(() => LyricsPage(song: playlist[currentIndex])),
        ),
        IconButton(
          icon: const Icon(Icons.more_vert, color: Colors.white),
          onPressed: () => _showMoreOptions(),
        ),
      ],
    );
  }

  void _showMoreOptions() {
    Get.bottomSheet(
      Container(
        padding: const EdgeInsets.all(16),
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: Icon(isFavorite ? Icons.favorite : Icons.favorite_border),
              title: Text(isFavorite ? 'إزالة من المفضلة' : 'إضافة للمفضلة'),
              onTap: () {
                Get.back();
                _toggleFavorite();
              },
            ),
            ListTile(
              leading: const Icon(Icons.share),
              title: const Text('مشاركة'),
              onTap: () {
                Get.back();
                _shareAudio();
              },
            ),
            ListTile(
              leading: const Icon(Icons.info),
              title: const Text('معلومات الملف'),
              onTap: () {
                Get.back();
                _showFileInfo();
              },
            ),
          ],
        ),
      ),
    );
  }
}
