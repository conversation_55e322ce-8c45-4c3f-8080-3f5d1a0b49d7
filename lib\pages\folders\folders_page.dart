import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:social_media_app/%20controllers/media_controller.dart';
import 'package:social_media_app/model/media_item.dart';
import 'package:social_media_app/pages/audio_player_page.dart';
import '../../widgets/media_card.dart';
import '../../model/folder_model.dart';
import '../video_player_page.dart';

class FoldersPage extends StatelessWidget {
  const FoldersPage({super.key});

  @override
  Widget build(BuildContext context) {
    return DefaultTabController(
      length: 2,
      child: Scaffold(
        appBar: AppBar(
          title: const Text('المجلدات'),
          backgroundColor: Colors.transparent,
          elevation: 0,
          flexibleSpace: Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [Colors.blue.shade800, Colors.purple.shade600],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
            ),
          ),
          bottom: const TabBar(
            indicatorColor: Colors.white,
            labelColor: Colors.white,
            unselectedLabelColor: Colors.white70,
            tabs: [
              Tab(icon: Icon(Icons.music_note), text: 'مجلدات الصوت'),
              Tab(icon: Icon(Icons.video_library), text: 'مجلدات الفيديو'),
            ],
          ),
        ),
        body: const TabBarView(
          children: [
            AudioFoldersTab(),
            VideoFoldersTab(),
          ],
        ),
      ),
    );
  }
}

class AudioFoldersTab extends StatelessWidget {
  const AudioFoldersTab({super.key});

  @override
  Widget build(BuildContext context) {
    final mediaController = Get.find<MediaController>();

    return Obx(() {
      if (mediaController.isLoadingAudio.value) {
        return const Center(child: CircularProgressIndicator());
      }

      if (mediaController.audioFolders.isEmpty) {
        return const Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.folder_open, size: 64, color: Colors.grey),
              SizedBox(height: 16),
              Text('لا توجد مجلدات صوتية', style: TextStyle(fontSize: 18)),
            ],
          ),
        );
      }

      return ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: mediaController.audioFolders.length,
        itemBuilder: (context, index) {
          final folder = mediaController.audioFolders[index];
          return _buildFolderCard(context, folder);
        },
      );
    });
  }

  Widget _buildFolderCard(BuildContext context, FolderModel folder) {
    return Card(
      elevation: 4,
      margin: const EdgeInsets.only(bottom: 12),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        onTap: () => _openFolder(context, folder),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              Container(
                width: 60,
                height: 60,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(12),
                  gradient: LinearGradient(
                    colors: [Colors.blue.shade300, Colors.blue.shade600],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                ),
                child: const Icon(Icons.folder, color: Colors.white, size: 32),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      folder.displayName,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 4),
                    Text(
                      '${folder.itemCount} ملف صوتي',
                      style: TextStyle(
                        color: Colors.grey.shade600,
                        fontSize: 14,
                      ),
                    ),
                    Text(
                      folder.path,
                      style: TextStyle(
                        color: Colors.grey.shade500,
                        fontSize: 12,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),
              Icon(Icons.arrow_forward_ios, color: Colors.grey.shade400),
            ],
          ),
        ),
      ),
    );
  }

  void _openFolder(BuildContext context, FolderModel folder) {
    Get.to(() => FolderContentPage(folder: folder));
  }
}

class VideoFoldersTab extends StatelessWidget {
  const VideoFoldersTab({super.key});

  @override
  Widget build(BuildContext context) {
    final mediaController = Get.find<MediaController>();

    return Obx(() {
      if (mediaController.isLoadingVideo.value) {
        return const Center(child: CircularProgressIndicator());
      }

      if (mediaController.videoFolders.isEmpty) {
        return const Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.folder_open, size: 64, color: Colors.grey),
              SizedBox(height: 16),
              Text('لا توجد مجلدات فيديو', style: TextStyle(fontSize: 18)),
            ],
          ),
        );
      }

      return ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: mediaController.videoFolders.length,
        itemBuilder: (context, index) {
          final folder = mediaController.videoFolders[index];
          return _buildFolderCard(context, folder);
        },
      );
    });
  }

  Widget _buildFolderCard(BuildContext context, FolderModel folder) {
    return Card(
      elevation: 4,
      margin: const EdgeInsets.only(bottom: 12),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        onTap: () => _openFolder(context, folder),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              Container(
                width: 60,
                height: 60,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(12),
                  gradient: LinearGradient(
                    colors: [Colors.purple.shade300, Colors.purple.shade600],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                ),
                child: const Icon(Icons.video_library,
                    color: Colors.white, size: 32),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      folder.displayName,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 4),
                    Text(
                      '${folder.itemCount} ملف فيديو',
                      style: TextStyle(
                        color: Colors.grey.shade600,
                        fontSize: 14,
                      ),
                    ),
                    Text(
                      folder.path,
                      style: TextStyle(
                        color: Colors.grey.shade500,
                        fontSize: 12,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),
              Icon(Icons.arrow_forward_ios, color: Colors.grey.shade400),
            ],
          ),
        ),
      ),
    );
  }

  void _openFolder(BuildContext context, FolderModel folder) {
    Get.to(() => FolderContentPage(folder: folder));
  }
}

class FolderContentPage extends StatelessWidget {
  final FolderModel folder;

  const FolderContentPage({super.key, required this.folder});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(folder.displayName),
        backgroundColor: Colors.transparent,
        elevation: 0,
        flexibleSpace: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: folder.type == MediaType.audio
                  ? [Colors.blue.shade800, Colors.blue.shade600]
                  : [Colors.purple.shade800, Colors.purple.shade600],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
          ),
        ),
      ),
      body: folder.items.isEmpty
          ? const Center(
              child: Text('لا توجد ملفات في هذا المجلد'),
            )
          : ListView.builder(
              padding: const EdgeInsets.all(16),
              itemCount: folder.items.length,
              itemBuilder: (context, index) {
                final item = folder.items[index];
                return MediaCard(
                  item: item,
                  onTap: () => _playMedia(item),
                  showMoreOptions: true,
                );
              },
            ),
    );
  }

  void _playMedia(item) {
    final mediaController = Get.find<MediaController>();
    mediaController.addToRecent(item);

    if (item.type == MediaType.audio) {
      Get.to(() => UnifiedAudioPlayerPage(audioFile: item.file));
    } else {
      Get.to(() => VideoPlayerPage(videoFile: item.file));
    }
  }
}
