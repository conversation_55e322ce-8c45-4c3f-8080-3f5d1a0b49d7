import 'dart:io';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:sliding_up_panel/sliding_up_panel.dart';
import 'package:social_media_app/%20controllers/audio_controller.dart';
import 'package:social_media_app/%20controllers/media_controller.dart';

import 'package:social_media_app/pages/favorites_playlists_page.dart';
import 'package:social_media_app/pages/recent_page.dart';
import '../widgets/media_card.dart';
import '../widgets/gradient_background.dart';
import '../widgets/mini_player.dart';
import '../widgets/full_player.dart';

class AudioHomePage extends StatelessWidget {
  const AudioHomePage({super.key});

  @override
  Widget build(BuildContext context) {
    final mediaController = Get.find<MediaController>();
    final audioController = Get.find<AudioController>();
    final PanelController panelController = PanelController();

    return Scaffold(
      body: Stack(
        children: [
          GradientBackground(
            child: CustomScrollView(
              slivers: [
                SliverAppBar(
                  expandedHeight: 200,
                  floating: false,
                  pinned: true,
                  backgroundColor: Colors.transparent,
                  flexibleSpace: FlexibleSpaceBar(
                    title: const Text(
                      'مكتبة الصوت',
                      style: TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    background: Container(
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: [Colors.blue.shade800, Colors.purple.shade600],
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                        ),
                      ),
                      child: const Center(
                        child: Icon(
                          Icons.music_note,
                          size: 80,
                          color: Colors.white54,
                        ),
                      ),
                    ),
                  ),
                ),
                SliverToBoxAdapter(
                  child: Padding(
                    padding: EdgeInsets.only(
                      left: 16,
                      right: 16,
                      top: 16,
                      bottom: audioController.hasCurrentTrack ? 90 : 16,
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        _buildQuickActions(context, audioController, mediaController),
                        const SizedBox(height: 24),
                        _buildRecentSection(mediaController, audioController),
                        const SizedBox(height: 24),
                        _buildAllSongsSection(mediaController, audioController),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
          
          // المشغل المصغر والكامل
          Obx(() {
            if (!audioController.hasCurrentTrack) {
              return const SizedBox.shrink();
            }

            return SlidingUpPanel(
              controller: panelController,
              minHeight: 70,
              maxHeight: MediaQuery.of(context).size.height * 0.9,
              borderRadius: const BorderRadius.vertical(top: Radius.circular(16)),
              panel: FullPlayer(panelController: panelController),
              collapsed: MiniPlayer(panelController: panelController),
              body: const SizedBox.shrink(),
            );
          }),
        ],
      ),
    );
  }

  Widget _buildQuickActions(BuildContext context, AudioController audioController, MediaController mediaController) {
    return Card(
      elevation: 8,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'الإجراءات السريعة',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                _buildActionButton(
                  icon: Icons.shuffle,
                  label: 'تشغيل عشوائي',
                  onTap: () => _shuffleAllSongs(audioController, mediaController),
                ),
                _buildActionButton(
                  icon: Icons.favorite,
                  label: 'المفضلة',
                  onTap: () => _showFavorites(),
                ),
                _buildActionButton(
                  icon: Icons.history,
                  label: 'المشغلة مؤخراً',
                  onTap: () => _showRecent(),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
        child: Column(
          children: [
            Icon(icon, size: 32, color: Colors.blue),
            const SizedBox(height: 8),
            Text(
              label,
              style: const TextStyle(fontSize: 12),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRecentSection(MediaController mediaController, AudioController audioController) {
    return Obx(() {
      if (mediaController.recentAudio.isEmpty) {
        return const SizedBox.shrink();
      }

      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'المشغلة مؤخراً',
            style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 12),
          SizedBox(
            height: 200,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: mediaController.recentAudio.length.clamp(0, 10),
              itemBuilder: (context, index) {
                final item = mediaController.recentAudio[index];
                return Container(
                  width: 160,
                  margin: const EdgeInsets.only(right: 12),
                  child: MediaCard(
                    item: item,
                    onTap: () => _playAudio(item, audioController, mediaController),
                  ),
                );
              },
            ),
          ),
        ],
      );
    });
  }

  Widget _buildAllSongsSection(MediaController mediaController, AudioController audioController) {
    return Obx(() {
      if (mediaController.isLoadingAudio.value) {
        return const Center(child: CircularProgressIndicator());
      }

      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                'جميع الأغاني',
                style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
              ),
              Text(
                '${mediaController.allAudioFiles.length} أغنية',
                style: TextStyle(color: Colors.grey.shade600),
              ),
            ],
          ),
          const SizedBox(height: 12),
          ListView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: mediaController.allAudioFiles.length,
            itemBuilder: (context, index) {
              final item = mediaController.allAudioFiles[index];
              return MediaCard(
                item: item,
                onTap: () => _playAudio(item, audioController, mediaController),
                showMoreOptions: true,
              );
            },
          ),
        ],
      );
    });
  }

  void _shuffleAllSongs(AudioController audioController, MediaController mediaController) {
    if (mediaController.allAudioFiles.isNotEmpty) {
      final shuffled = List.from(mediaController.allAudioFiles)..shuffle();
      audioController.setPlaylist(shuffled, 0);
      audioController.playMediaItem(shuffled.first);
    }
  }

  void _showFavorites() {
    Get.to(() => const FavoritesPage());
  }

  void _showRecent() {
    Get.to(() => const RecentPage());
  }

  void _playAudio(item, AudioController audioController, MediaController mediaController) {
    mediaController.addToRecent(item);
    audioController.setPlaylist(mediaController.allAudioFiles, 
        mediaController.allAudioFiles.indexOf(item));
    audioController.playMediaItem(item);
  }
}

