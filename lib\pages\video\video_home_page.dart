import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:social_media_app/%20controllers/media_controller.dart';
import '../../widgets/media_card.dart';
import '../../widgets/gradient_background.dart';
import '../video_player_page.dart';

class VideoHomePage extends StatelessWidget {
  const VideoHomePage({super.key});

  @override
  Widget build(BuildContext context) {
    final mediaController = Get.find<MediaController>();

    return Scaffold(
      body: GradientBackground(
        child: CustomScrollView(
          slivers: [
            SliverAppBar(
              expandedHeight: 200,
              floating: false,
              pinned: true,
              backgroundColor: Colors.transparent,
              flexibleSpace: FlexibleSpaceBar(
                title: Text(
                  'مكتبة الفيديو',
                  style: TextStyle(
                    color: Get.theme.colorScheme.onPrimary,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                background: Container(
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        Get.theme.colorScheme.primary,
                        Get.theme.colorScheme.secondary
                      ],
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    ),
                  ),
                  child: Center(
                    child: Icon(
                      Icons.video_library,
                      size: 80,
                      color: Get.theme.colorScheme.onPrimary
                          .withValues(alpha: 0.5),
                    ),
                  ),
                ),
              ),
            ),
            SliverToBoxAdapter(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildQuickActions(context),
                    const SizedBox(height: 24),
                    _buildRecentSection(mediaController),
                    const SizedBox(height: 24),
                    _buildAllVideosSection(mediaController),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickActions(BuildContext context) {
    return Card(
      elevation: 8,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'الإجراءات السريعة',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                _buildActionButton(
                  icon: Icons.shuffle,
                  label: 'تشغيل عشوائي',
                  onTap: () => _shuffleAllVideos(),
                ),
                _buildActionButton(
                  icon: Icons.favorite,
                  label: 'المفضلة',
                  onTap: () => _showFavorites(),
                ),
                _buildActionButton(
                  icon: Icons.history,
                  label: 'المشاهدة مؤخراً',
                  onTap: () => _showRecent(),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          color: Colors.purple.shade50,
        ),
        child: Column(
          children: [
            Icon(icon, size: 32, color: Colors.purple.shade700),
            const SizedBox(height: 8),
            Text(
              label,
              style: TextStyle(
                fontSize: 12,
                color: Colors.purple.shade700,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRecentSection(MediaController controller) {
    return Obx(() {
      if (controller.recentVideo.isEmpty) {
        return const SizedBox.shrink();
      }

      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'المشاهدة مؤخراً',
            style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 12),
          SizedBox(
            height: 200,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: controller.recentVideo.length.clamp(0, 10),
              itemBuilder: (context, index) {
                final item = controller.recentVideo[index];
                return Container(
                  width: 160,
                  margin: const EdgeInsets.only(right: 12),
                  child: MediaCard(
                    item: item,
                    onTap: () => _playVideo(item),
                  ),
                );
              },
            ),
          ),
        ],
      );
    });
  }

  Widget _buildAllVideosSection(MediaController controller) {
    return Obx(() {
      if (controller.isLoadingVideo.value) {
        return const Center(
          child: CircularProgressIndicator(),
        );
      }

      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                'جميع الفيديوهات',
                style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
              ),
              Text(
                '${controller.allVideoFiles.length} فيديو',
                style: TextStyle(color: Colors.grey.shade600),
              ),
            ],
          ),
          const SizedBox(height: 12),
          GridView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 2,
              childAspectRatio: 16 / 12,
              crossAxisSpacing: 12,
              mainAxisSpacing: 12,
            ),
            itemCount: controller.allVideoFiles.length,
            itemBuilder: (context, index) {
              final item = controller.allVideoFiles[index];
              return MediaCard(
                item: item,
                onTap: () => _playVideo(item),
                isGridView: true,
              );
            },
          ),
        ],
      );
    });
  }

  void _shuffleAllVideos() {
    final controller = Get.find<MediaController>();
    if (controller.allVideoFiles.isNotEmpty) {
      controller.allVideoFiles.shuffle();
      _playVideo(controller.allVideoFiles.first);
    }
  }

  void _showFavorites() {
    // Navigate to favorites page
  }

  void _showRecent() {
    // Navigate to recent page
  }

  void _playVideo(item) {
    final controller = Get.find<MediaController>();
    controller.addToRecent(item);
    Get.to(() => VideoPlayerPage(videoFile: item.file));
  }
}
